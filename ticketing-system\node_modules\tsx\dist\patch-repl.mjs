var i=Object.defineProperty;var a=(t,r)=>i(t,"name",{value:r,configurable:!0});import s from"node:repl";import{transform as f}from"esbuild";const p=a(t=>{const{eval:r}=t,n=a(async function(e,c,o,l){try{e=(await f(e,{sourcefile:o,loader:"ts",tsconfigRaw:{compilerOptions:{preserveValueImports:!0}},define:{require:"global.require"}})).code}catch{}return r.call(this,e,c,o,l)},"preEval");t.eval=n},"patchEval"),{start:u}=s;s.start=function(){const t=Reflect.apply(u,this,arguments);return p(t),t};
