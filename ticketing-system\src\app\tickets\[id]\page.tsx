import { notFound } from 'next/navigation'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { TicketDetails } from '@/components/tickets/ticket-details'
import { UserRole } from '@prisma/client'

interface TicketPageProps {
  params: { id: string }
}

export default async function TicketPage({ params }: TicketPageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session) {
    return notFound()
  }

  const ticket = await db.ticket.findUnique({
    where: { id: params.id },
    include: {
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          department: true,
        },
      },
      assignedTo: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          department: true,
        },
      },
      comments: {
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      },
      attachments: true,
    },
  })

  if (!ticket) {
    return notFound()
  }

  // Check if user can view this ticket
  const canView = 
    session.user.role === UserRole.ADMIN ||
    session.user.role === UserRole.AGENT ||
    ticket.createdById === session.user.id ||
    ticket.assignedToId === session.user.id

  if (!canView) {
    return notFound()
  }

  return (
    <DashboardLayout>
      <TicketDetails ticket={ticket} currentUser={session.user} />
    </DashboardLayout>
  )
}
