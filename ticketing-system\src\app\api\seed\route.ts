import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import bcrypt from 'bcryptjs'
import { UserRole, TicketStatus, TicketPriority } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    // Check if users already exist
    const existingUsers = await db.user.count()
    if (existingUsers > 0) {
      return NextResponse.json({ message: 'Database already seeded' })
    }

    // Create users
    const adminPassword = await bcrypt.hash('admin123', 12)
    const agentPassword = await bcrypt.hash('agent123', 12)
    const userPassword = await bcrypt.hash('user123', 12)

    const admin = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'System Administrator',
        password: adminPassword,
        role: UserRole.ADMIN,
        department: 'IT',
        phone: '******-0101',
      },
    })

    const agent = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Support Agent',
        password: agentPassword,
        role: UserRole.AGENT,
        department: 'IT Support',
        phone: '******-0102',
      },
    })

    const user = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'John Doe',
        password: userPassword,
        role: UserRole.USER,
        department: 'Marketing',
        phone: '******-0103',
      },
    })

    // Create sample tickets
    const tickets = [
      {
        title: 'Cannot access email account',
        description: 'I am unable to log into my email account. Getting authentication error.',
        status: TicketStatus.OPEN,
        priority: TicketPriority.HIGH,
        category: 'Email',
        createdById: user.id,
        assignedToId: agent.id,
      },
      {
        title: 'Printer not working',
        description: 'The office printer is not responding. Paper jam error showing.',
        status: TicketStatus.IN_PROGRESS,
        priority: TicketPriority.MEDIUM,
        category: 'Hardware',
        createdById: user.id,
        assignedToId: agent.id,
      },
      {
        title: 'Software installation request',
        description: 'Need Adobe Creative Suite installed on my workstation.',
        status: TicketStatus.PENDING,
        priority: TicketPriority.LOW,
        category: 'Software',
        createdById: user.id,
      },
      {
        title: 'Network connectivity issues',
        description: 'Intermittent network disconnections throughout the day.',
        status: TicketStatus.RESOLVED,
        priority: TicketPriority.URGENT,
        category: 'Network',
        createdById: user.id,
        assignedToId: agent.id,
        resolvedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
    ]

    for (const ticketData of tickets) {
      const ticket = await db.ticket.create({
        data: ticketData,
      })

      // Add some comments to tickets
      if (ticket.status !== TicketStatus.OPEN) {
        await db.comment.create({
          data: {
            content: 'Thank you for reporting this issue. I will look into it.',
            ticketId: ticket.id,
            authorId: agent.id,
            isInternal: false,
          },
        })

        if (ticket.status === TicketStatus.RESOLVED) {
          await db.comment.create({
            data: {
              content: 'Issue has been resolved. Please let me know if you need further assistance.',
              ticketId: ticket.id,
              authorId: agent.id,
              isInternal: false,
            },
          })
        }
      }
    }

    return NextResponse.json({
      message: 'Database seeded successfully!',
      accounts: {
        admin: '<EMAIL> / admin123',
        agent: '<EMAIL> / agent123',
        user: '<EMAIL> / user123',
      },
    })
  } catch (error) {
    console.error('Seeding error:', error)
    return NextResponse.json(
      { error: 'Failed to seed database' },
      { status: 500 }
    )
  }
}
