/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/auth-provider.tsx */ \"(rsc)/./src/components/providers/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN4YW1wcCU1QyU1Q2h0ZG9jcyU1QyU1Q3Rlc3R4JTVDJTVDdGlja2V0aW5nLXN5c3RlbSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDeGFtcHAlNUMlNUNodGRvY3MlNUMlNUN0ZXN0eCU1QyU1Q3RpY2tldGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDeGFtcHAlNUMlNUNodGRvY3MlNUMlNUN0ZXN0eCU1QyU1Q3RpY2tldGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXHRlc3R4XFxcXHRpY2tldGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/dashboard-stats.tsx */ \"(rsc)/./src/components/dashboard/dashboard-stats.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/recent-tickets.tsx */ \"(rsc)/./src/components/dashboard/recent-tickets.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/ticket-chart.tsx */ \"(rsc)/./src/components/dashboard/ticket-chart.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/dashboard-layout.tsx */ \"(rsc)/./src/components/layout/dashboard-layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(rsc)/./src/components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(rsc)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_dashboard_recent_tickets__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recent-tickets */ \"(rsc)/./src/components/dashboard/recent-tickets.tsx\");\n/* harmony import */ var _components_dashboard_ticket_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/ticket-chart */ \"(rsc)/./src/components/dashboard/ticket-chart.tsx\");\n\n\n\n\n\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Welcome to your ServiceDesk Pro dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_2__.DashboardStats, {}, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ticket_chart__WEBPACK_IMPORTED_MODULE_4__.TicketChart, {}, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_tickets__WEBPACK_IMPORTED_MODULE_3__.RecentTickets, {}, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRTtBQUNDO0FBQ0Y7QUFDSjtBQUVsRCxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osZ0ZBQWVBO2tCQUNkLDRFQUFDSztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7O3NDQUNDLDhEQUFDRTs0QkFBR0QsV0FBVTtzQ0FBbUM7Ozs7OztzQ0FDakQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7OzhCQUcvQiw4REFBQ0wsaUZBQWNBOzs7Ozs4QkFFZiw4REFBQ0k7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSCwyRUFBV0E7Ozs7O3NDQUNaLDhEQUFDRCwrRUFBYUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLeEIiLCJzb3VyY2VzIjpbIkQ6XFx4YW1wcFxcaHRkb2NzXFx0ZXN0eFxcdGlja2V0aW5nLXN5c3RlbVxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRGFzaGJvYXJkTGF5b3V0IH0gZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9kYXNoYm9hcmQtbGF5b3V0J1xuaW1wb3J0IHsgRGFzaGJvYXJkU3RhdHMgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC1zdGF0cydcbmltcG9ydCB7IFJlY2VudFRpY2tldHMgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3JlY2VudC10aWNrZXRzJ1xuaW1wb3J0IHsgVGlja2V0Q2hhcnQgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3RpY2tldC1jaGFydCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8RGFzaGJvYXJkTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5EYXNoYm9hcmQ8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5XZWxjb21lIHRvIHlvdXIgU2VydmljZURlc2sgUHJvIGRhc2hib2FyZDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8RGFzaGJvYXJkU3RhdHMgLz5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIDxUaWNrZXRDaGFydCAvPlxuICAgICAgICAgIDxSZWNlbnRUaWNrZXRzIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9EYXNoYm9hcmRMYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEYXNoYm9hcmRMYXlvdXQiLCJEYXNoYm9hcmRTdGF0cyIsIlJlY2VudFRpY2tldHMiLCJUaWNrZXRDaGFydCIsIkRhc2hib2FyZFBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5d19dd2c37f7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxceGFtcHBcXGh0ZG9jc1xcdGVzdHhcXHRpY2tldGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVkMTlkZDJjMzdmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./src/components/providers/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'ServiceDesk Pro - Professional Ticketing System',\n    description: 'A modern, professional ticketing system similar to ServiceNow'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzZDO0FBSTVELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNkVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxceGFtcHBcXGh0ZG9jc1xcdGVzdHhcXHRpY2tldGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycy9hdXRoLXByb3ZpZGVyJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU2VydmljZURlc2sgUHJvIC0gUHJvZmVzc2lvbmFsIFRpY2tldGluZyBTeXN0ZW0nLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuLCBwcm9mZXNzaW9uYWwgdGlja2V0aW5nIHN5c3RlbSBzaW1pbGFyIHRvIFNlcnZpY2VOb3cnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardStats: () => (/* binding */ DashboardStats)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DashboardStats = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DashboardStats() from the server but DashboardStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\xampp\\htdocs\\testx\\ticketing-system\\src\\components\\dashboard\\dashboard-stats.tsx",
"DashboardStats",
);

/***/ }),

/***/ "(rsc)/./src/components/dashboard/recent-tickets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/recent-tickets.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RecentTickets: () => (/* binding */ RecentTickets)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const RecentTickets = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call RecentTickets() from the server but RecentTickets is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\xampp\\htdocs\\testx\\ticketing-system\\src\\components\\dashboard\\recent-tickets.tsx",
"RecentTickets",
);

/***/ }),

/***/ "(rsc)/./src/components/dashboard/ticket-chart.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/ticket-chart.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TicketChart: () => (/* binding */ TicketChart)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TicketChart = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TicketChart() from the server but TicketChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\xampp\\htdocs\\testx\\ticketing-system\\src\\components\\dashboard\\ticket-chart.tsx",
"TicketChart",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DashboardLayout = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\xampp\\htdocs\\testx\\ticketing-system\\src\\components\\layout\\dashboard-layout.tsx",
"DashboardLayout",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\xampp\\htdocs\\testx\\ticketing-system\\src\\components\\providers\\auth-provider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/auth-provider.tsx */ \"(ssr)/./src/components/providers/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUN4YW1wcCU1QyU1Q2h0ZG9jcyU1QyU1Q3Rlc3R4JTVDJTVDdGlja2V0aW5nLXN5c3RlbSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDeGFtcHAlNUMlNUNodGRvY3MlNUMlNUN0ZXN0eCU1QyU1Q3RpY2tldGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDeGFtcHAlNUMlNUNodGRvY3MlNUMlNUN0ZXN0eCU1QyU1Q3RpY2tldGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXHRlc3R4XFxcXHRpY2tldGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/dashboard-stats.tsx */ \"(ssr)/./src/components/dashboard/dashboard-stats.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/recent-tickets.tsx */ \"(ssr)/./src/components/dashboard/recent-tickets.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/ticket-chart.tsx */ \"(ssr)/./src/components/dashboard/ticket-chart.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/dashboard-layout.tsx */ \"(ssr)/./src/components/layout/dashboard-layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-stats.tsx%22%2C%22ids%22%3A%5B%22DashboardStats%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Crecent-tickets.tsx%22%2C%22ids%22%3A%5B%22RecentTickets%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cticket-chart.tsx%22%2C%22ids%22%3A%5B%22TicketChart%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Ctestx%5C%5Cticketing-system%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cdashboard-layout.tsx%22%2C%22ids%22%3A%5B%22DashboardLayout%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStats: () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Ticket,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardStats auto */ \n\n\n\n\nfunction DashboardStats() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalTickets: 0,\n        openTickets: 0,\n        inProgressTickets: 0,\n        resolvedTickets: 0,\n        myTickets: 0,\n        urgentTickets: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardStats.useEffect\": ()=>{\n            fetchStats();\n        }\n    }[\"DashboardStats.useEffect\"], []);\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/dashboard/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data);\n            }\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const statCards = [\n        {\n            title: 'Total Tickets',\n            value: stats.totalTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-100'\n        },\n        {\n            title: 'Open Tickets',\n            value: stats.openTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-orange-600',\n            bgColor: 'bg-orange-100'\n        },\n        {\n            title: 'In Progress',\n            value: stats.inProgressTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-yellow-600',\n            bgColor: 'bg-yellow-100'\n        },\n        {\n            title: 'Resolved',\n            value: stats.resolvedTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'text-green-600',\n            bgColor: 'bg-green-100'\n        },\n        {\n            title: 'My Tickets',\n            value: stats.myTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-100'\n        },\n        {\n            title: 'Urgent',\n            value: stats.urgentTickets,\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Ticket_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: 'text-red-600',\n            bgColor: 'bg-red-100'\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n            children: Array.from({\n                length: 6\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this)\n                }, i, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n        children: statCards.map((stat, index)=>{\n            const Icon = stat.icon;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: stat.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-3 rounded-full ${stat.bgColor}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: `w-6 h-6 ${stat.color}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 13\n                }, this)\n            }, index, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n                lineNumber: 121,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\dashboard-stats.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/dashboard-stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/recent-tickets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/recent-tickets.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentTickets: () => (/* binding */ RecentTickets)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentTickets auto */ \n\n\n\n\n\nfunction RecentTickets() {\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentTickets.useEffect\": ()=>{\n            fetchRecentTickets();\n        }\n    }[\"RecentTickets.useEffect\"], []);\n    const fetchRecentTickets = async ()=>{\n        try {\n            const response = await fetch('/api/tickets?limit=5');\n            if (response.ok) {\n                const data = await response.json();\n                setTickets(data.tickets);\n            }\n        } catch (error) {\n            console.error('Error fetching recent tickets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return 'bg-blue-100 text-blue-800';\n            case 'IN_PROGRESS':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'PENDING':\n                return 'bg-orange-100 text-orange-800';\n            case 'RESOLVED':\n                return 'bg-green-100 text-green-800';\n            case 'CLOSED':\n                return 'bg-gray-100 text-gray-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'LOW':\n                return 'bg-gray-100 text-gray-800';\n            case 'MEDIUM':\n                return 'bg-blue-100 text-blue-800';\n            case 'HIGH':\n                return 'bg-orange-100 text-orange-800';\n            case 'URGENT':\n                return 'bg-red-100 text-red-800';\n            case 'CRITICAL':\n                return 'bg-red-200 text-red-900';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"Recent Tickets\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: Array.from({\n                            length: 5\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    children: \"Recent Tickets\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: [\n                    tickets.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-center py-4\",\n                        children: \"No tickets found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: tickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 pb-4 last:border-b-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/tickets/${ticket.id}`,\n                                                className: \"text-sm font-medium text-gray-900 hover:text-primary\",\n                                                children: ticket.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    \"Created by \",\n                                                    ticket.createdBy.name,\n                                                    \" • \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatRelativeTime)(ticket.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: getStatusColor(ticket.status),\n                                                        children: ticket.status.replace('_', ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: getPriorityColor(ticket.priority),\n                                                        children: ticket.priority\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this)\n                            }, ticket.id, false, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/tickets\",\n                            className: \"text-sm text-primary hover:text-primary/80 font-medium\",\n                            children: \"View all tickets →\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\recent-tickets.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvcmVjZW50LXRpY2tldHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2Y7QUFDbUQ7QUFDbEM7QUFDRztBQUd6QyxTQUFTUztJQUNkLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBd0IsRUFBRTtJQUNoRSxNQUFNLENBQUNXLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQTttQ0FBQztZQUNSYztRQUNGO2tDQUFHLEVBQUU7SUFFTCxNQUFNQSxxQkFBcUI7UUFDekIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQ1IsV0FBV08sS0FBS1IsT0FBTztZQUN6QjtRQUNGLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtRQUNsRCxTQUFVO1lBQ1JQLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTVMsaUJBQWlCLENBQUNDO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLElBQUliLFNBQVM7UUFDWCxxQkFDRSw4REFBQ1QscURBQUlBOzs4QkFDSCw4REFBQ0UsMkRBQVVBOzhCQUNULDRFQUFDQywwREFBU0E7a0NBQUM7Ozs7Ozs7Ozs7OzhCQUViLDhEQUFDRiw0REFBV0E7OEJBQ1YsNEVBQUNzQjt3QkFBSUMsV0FBVTtrQ0FDWkMsTUFBTUMsSUFBSSxDQUFDOzRCQUFFQyxRQUFRO3dCQUFFLEdBQUdDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDakMsOERBQUNQO2dDQUFZQyxXQUFVOztrREFDckIsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7OytCQUZQTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBU3RCO0lBRUEscUJBQ0UsOERBQUM5QixxREFBSUE7OzBCQUNILDhEQUFDRSwyREFBVUE7MEJBQ1QsNEVBQUNDLDBEQUFTQTs4QkFBQzs7Ozs7Ozs7Ozs7MEJBRWIsOERBQUNGLDREQUFXQTs7b0JBQ1RNLFFBQVFvQixNQUFNLEtBQUssa0JBQ2xCLDhEQUFDSTt3QkFBRVAsV0FBVTtrQ0FBaUM7Ozs7OzZDQUU5Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pqQixRQUFRcUIsR0FBRyxDQUFDLENBQUNJLHVCQUNaLDhEQUFDVDtnQ0FBb0JDLFdBQVU7MENBQzdCLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDekIsa0RBQUlBO2dEQUNIa0MsTUFBTSxDQUFDLFNBQVMsRUFBRUQsT0FBT0UsRUFBRSxFQUFFO2dEQUM3QlYsV0FBVTswREFFVFEsT0FBT0csS0FBSzs7Ozs7OzBEQUVmLDhEQUFDSjtnREFBRVAsV0FBVTs7b0RBQTZCO29EQUM1QlEsT0FBT0ksU0FBUyxDQUFDQyxJQUFJO29EQUFDO29EQUFJaEMsOERBQWtCQSxDQUFDMkIsT0FBT00sU0FBUzs7Ozs7OzswREFFM0UsOERBQUNmO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3BCLHVEQUFLQTt3REFBQ29CLFdBQVdMLGVBQWVhLE9BQU9aLE1BQU07a0VBQzNDWSxPQUFPWixNQUFNLENBQUNtQixPQUFPLENBQUMsS0FBSzs7Ozs7O2tFQUU5Qiw4REFBQ25DLHVEQUFLQTt3REFBQ29CLFdBQVdILGlCQUFpQlcsT0FBT1YsUUFBUTtrRUFDL0NVLE9BQU9WLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWpCaEJVLE9BQU9FLEVBQUU7Ozs7Ozs7Ozs7a0NBMEJ6Qiw4REFBQ1g7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN6QixrREFBSUE7NEJBQ0hrQyxNQUFLOzRCQUNMVCxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YIiwic291cmNlcyI6WyJEOlxceGFtcHBcXGh0ZG9jc1xcdGVzdHhcXHRpY2tldGluZy1zeXN0ZW1cXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxyZWNlbnQtdGlja2V0cy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmVUaW1lIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5pbXBvcnQgeyBUaWNrZXRXaXRoUmVsYXRpb25zIH0gZnJvbSAnQC90eXBlcydcblxuZXhwb3J0IGZ1bmN0aW9uIFJlY2VudFRpY2tldHMoKSB7XG4gIGNvbnN0IFt0aWNrZXRzLCBzZXRUaWNrZXRzXSA9IHVzZVN0YXRlPFRpY2tldFdpdGhSZWxhdGlvbnNbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFJlY2VudFRpY2tldHMoKVxuICB9LCBbXSlcblxuICBjb25zdCBmZXRjaFJlY2VudFRpY2tldHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdGlja2V0cz9saW1pdD01JylcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHNldFRpY2tldHMoZGF0YS50aWNrZXRzKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZWNlbnQgdGlja2V0czonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdPUEVOJzpcbiAgICAgICAgcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJ1xuICAgICAgY2FzZSAnSU5fUFJPR1JFU1MnOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJ1xuICAgICAgY2FzZSAnUEVORElORyc6XG4gICAgICAgIHJldHVybiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAnXG4gICAgICBjYXNlICdSRVNPTFZFRCc6XG4gICAgICAgIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgY2FzZSAnQ0xPU0VEJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFByaW9yaXR5Q29sb3IgPSAocHJpb3JpdHk6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAocHJpb3JpdHkpIHtcbiAgICAgIGNhc2UgJ0xPVyc6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgIGNhc2UgJ01FRElVTSc6XG4gICAgICAgIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCdcbiAgICAgIGNhc2UgJ0hJR0gnOlxuICAgICAgICByZXR1cm4gJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwJ1xuICAgICAgY2FzZSAnVVJHRU5UJzpcbiAgICAgICAgcmV0dXJuICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCdcbiAgICAgIGNhc2UgJ0NSSVRJQ0FMJzpcbiAgICAgICAgcmV0dXJuICdiZy1yZWQtMjAwIHRleHQtcmVkLTkwMCdcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZT5SZWNlbnQgVGlja2V0czwvQ2FyZFRpdGxlPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IDUgfSkubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0yMDAgcm91bmRlZCB3LTMvNCBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPENhcmQ+XG4gICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRUaXRsZT5SZWNlbnQgVGlja2V0czwvQ2FyZFRpdGxlPlxuICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICB7dGlja2V0cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LWNlbnRlciBweS00XCI+Tm8gdGlja2V0cyBmb3VuZDwvcD5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAge3RpY2tldHMubWFwKCh0aWNrZXQpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3RpY2tldC5pZH0gY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHBiLTQgbGFzdDpib3JkZXItYi0wXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPExpbmsgXG4gICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC90aWNrZXRzLyR7dGlja2V0LmlkfWB9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGhvdmVyOnRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7dGlja2V0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlZCBieSB7dGlja2V0LmNyZWF0ZWRCeS5uYW1lfSDigKIge2Zvcm1hdFJlbGF0aXZlVGltZSh0aWNrZXQuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT17Z2V0U3RhdHVzQ29sb3IodGlja2V0LnN0YXR1cyl9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3RpY2tldC5zdGF0dXMucmVwbGFjZSgnXycsICcgJyl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPXtnZXRQcmlvcml0eUNvbG9yKHRpY2tldC5wcmlvcml0eSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3RpY2tldC5wcmlvcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8TGluayBcbiAgICAgICAgICAgIGhyZWY9XCIvdGlja2V0c1wiIFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnkgaG92ZXI6dGV4dC1wcmltYXJ5LzgwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBWaWV3IGFsbCB0aWNrZXRzIOKGklxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTGluayIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsImZvcm1hdFJlbGF0aXZlVGltZSIsIlJlY2VudFRpY2tldHMiLCJ0aWNrZXRzIiwic2V0VGlja2V0cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZmV0Y2hSZWNlbnRUaWNrZXRzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJlcnJvciIsImNvbnNvbGUiLCJnZXRTdGF0dXNDb2xvciIsInN0YXR1cyIsImdldFByaW9yaXR5Q29sb3IiLCJwcmlvcml0eSIsImRpdiIsImNsYXNzTmFtZSIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwicCIsInRpY2tldCIsImhyZWYiLCJpZCIsInRpdGxlIiwiY3JlYXRlZEJ5IiwibmFtZSIsImNyZWF0ZWRBdCIsInJlcGxhY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/recent-tickets.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/ticket-chart.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/ticket-chart.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketChart: () => (/* binding */ TicketChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ TicketChart auto */ \n\n\nfunction TicketChart() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TicketChart.useEffect\": ()=>{\n            fetchChartData();\n        }\n    }[\"TicketChart.useEffect\"], []);\n    const fetchChartData = async ()=>{\n        try {\n            const response = await fetch('/api/dashboard/chart');\n            if (response.ok) {\n                const chartData = await response.json();\n                setData(chartData);\n            }\n        } catch (error) {\n            console.error('Error fetching chart data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        children: \"Tickets by Status\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse space-y-4\",\n                        children: Array.from({\n                            length: 4\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-4 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-4 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    const total = data.reduce((sum, item)=>sum + item.count, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    children: \"Tickets by Status\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: data.map((item)=>{\n                            const percentage = total > 0 ? item.count / total * 100 : 0;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 rounded\",\n                                        style: {\n                                            backgroundColor: item.color\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: item.status.replace('_', ' ')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: item.count\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 rounded-full\",\n                                                    style: {\n                                                        backgroundColor: item.color,\n                                                        width: `${percentage}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.status, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    total === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-center py-4\",\n                        children: \"No tickets found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\dashboard\\\\ticket-chart.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/ticket-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./navbar */ \"(ssr)/./src/components/layout/navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            if (status === 'loading') return; // Still loading\n            if (!session) router.push('/auth/signin');\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navbar__WEBPACK_IMPORTED_MODULE_4__.Navbar, {}, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,LogOut,Menu,Search,Settings,Ticket,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        roles: [\n            'USER',\n            'AGENT',\n            'ADMIN'\n        ]\n    },\n    {\n        name: 'Tickets',\n        href: '/tickets',\n        icon: _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        roles: [\n            'USER',\n            'AGENT',\n            'ADMIN'\n        ]\n    },\n    {\n        name: 'Users',\n        href: '/users',\n        icon: _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        roles: [\n            'AGENT',\n            'ADMIN'\n        ]\n    },\n    {\n        name: 'Settings',\n        href: '/settings',\n        icon: _barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        roles: [\n            'ADMIN'\n        ]\n    }\n];\nfunction Navbar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userRole = session?.user?.role;\n    const filteredNavigation = navigation.filter((item)=>userRole && item.roles.includes(userRole));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"text-xl font-bold text-primary\",\n                                        children: \"ServiceDesk Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                    children: filteredNavigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium', pathname === item.href ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            type: \"text\",\n                                            placeholder: \"Search tickets...\",\n                                            className: \"pl-10 w-64\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                children: session?.user?.name?.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: session?.user?.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: session?.user?.role\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                className: \"text-gray-500 hover:text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-2 pb-3 space-y-1\",\n                        children: filteredNavigation.map((item)=>{\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('flex items-center px-3 py-2 text-base font-medium', pathname === item.href ? 'bg-primary/10 border-r-4 border-primary text-primary' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'),\n                                onClick: ()=>setMobileMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.name\n                                ]\n                            }, item.name, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4 pb-3 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium\",\n                                        children: session?.user?.name?.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-base font-medium text-gray-800\",\n                                                children: session?.user?.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: session?.user?.email\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start px-4 py-2\",\n                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_LogOut_Menu_Search_Settings_Ticket_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign out\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvYXV0aC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBTTFDLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxxQkFBTyw4REFBQ0YsNERBQWVBO2tCQUFFRTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJEOlxceGFtcHBcXGh0ZG9jc1xcdGVzdHhcXHRpY2tldGluZy1zeXN0ZW1cXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxhdXRoLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuXG5pbnRlcmZhY2UgQXV0aFByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBBdXRoUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPFNlc3Npb25Qcm92aWRlcj57Y2hpbGRyZW59PC9TZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Badge({ className, variant = 'default', ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2', {\n            'border-transparent bg-primary text-primary-foreground hover:bg-primary/80': variant === 'default',\n            'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80': variant === 'destructive',\n            'text-foreground': variant === 'outline'\n        }, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = 'default', size = 'default', ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', {\n            'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',\n            'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',\n            'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            'text-primary underline-offset-4 hover:underline': variant === 'link'\n        }, {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon'\n        }, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-lg border bg-card text-card-foreground shadow-sm', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = 'Card';\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-col space-y-1.5 p-6', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = 'CardHeader';\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-2xl font-semibold leading-none tracking-tight', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = 'CardTitle';\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('text-sm text-muted-foreground', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = 'CardDescription';\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = 'CardContent';\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = 'CardFooter';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50', className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\xampp\\\\htdocs\\\\testx\\\\ticketing-system\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = 'Input';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiRDpcXHhhbXBwXFxodGRvY3NcXHRlc3R4XFx0aWNrZXRpbmctc3lzdGVtXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgJ2ZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCcsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSAnSW5wdXQnXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Date(date).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n}\nfunction formatDateTime(date) {\n    return new Date(date).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const targetDate = new Date(date);\n    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return 'just now';\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 2592000) {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else {\n        return formatDate(date);\n    }\n}\nfunction getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0).toUpperCase()).join('').slice(0, 2);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cxampp%5Chtdocs%5Ctestx%5Cticketing-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();