import"../../get-pipe-path-BHW2eJdv.mjs";import{r as j}from"../../register-CFH5oNdT.mjs";import{t as l}from"../../require-DQxpCAr4.mjs";import"module";import"node:path";import"../../temporary-directory-CwHp0_NW.mjs";import"node:os";import"node:module";import"node:url";import"get-tsconfig";import"node:fs";import"../../index-7AaEi15b.mjs";import"esbuild";import"node:crypto";import"../../client-BQVF1NaW.mjs";import"node:net";import"node:util";import"../../index-gbaejti9.mjs";export{j as register,l as require};
