import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { RecentTickets } from '@/components/dashboard/recent-tickets'
import { TicketChart } from '@/components/dashboard/ticket-chart'

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome to your ServiceDesk Pro dashboard</p>
        </div>
        
        <DashboardStats />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TicketChart />
          <RecentTickets />
        </div>
      </div>
    </DashboardLayout>
  )
}
