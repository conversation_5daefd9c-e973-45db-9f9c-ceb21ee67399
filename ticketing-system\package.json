{"name": "ticketing-system", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.11.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^15.3.5", "next-auth": "^4.24.11", "postcss": "^8.5.6", "prisma": "^6.11.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "ts-node": "^10.9.2", "tsx": "^4.20.3"}}