'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ArrowLeft, Calendar, User, Clock, Tag } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatDateTime, getInitials } from '@/lib/utils'
import { TicketWithRelations, CommentWithAuthor } from '@/types'
import { UserRole } from '@prisma/client'
// import { TicketActions } from './ticket-actions'
// import { CommentsList } from './comments-list'
// import { AddComment } from './add-comment'

interface TicketDetailsProps {
  ticket: TicketWithRelations
  currentUser: {
    id: string
    role: UserRole
    name?: string | null
  }
}

export function TicketDetails({ ticket, currentUser }: TicketDetailsProps) {
  const [comments, setComments] = useState<CommentWithAuthor[]>(ticket.comments)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-blue-100 text-blue-800'
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800'
      case 'PENDING':
        return 'bg-orange-100 text-orange-800'
      case 'RESOLVED':
        return 'bg-green-100 text-green-800'
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-gray-100 text-gray-800'
      case 'MEDIUM':
        return 'bg-blue-100 text-blue-800'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800'
      case 'URGENT':
        return 'bg-red-100 text-red-800'
      case 'CRITICAL':
        return 'bg-red-200 text-red-900'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const canEdit = 
    currentUser.role === UserRole.ADMIN ||
    currentUser.role === UserRole.AGENT ||
    ticket.createdById === currentUser.id

  const handleCommentAdded = (newComment: CommentWithAuthor) => {
    setComments(prev => [...prev, newComment])
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/tickets">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tickets
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{ticket.title}</h1>
            <p className="text-gray-600">Ticket #{ticket.id.slice(-8)}</p>
          </div>
        </div>
        {canEdit && (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              Edit Ticket
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="whitespace-pre-wrap">{ticket.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Comments */}
          <Card>
            <CardHeader>
              <CardTitle>Comments ({comments.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {comments.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No comments yet</p>
                ) : (
                  <div className="space-y-4">
                    {comments.map((comment) => (
                      <div key={comment.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium">
                            {getInitials(comment.author.name || '')}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium">{comment.author.name}</span>
                              <span className="text-xs text-gray-500">{formatDateTime(comment.createdAt)}</span>
                              {comment.isInternal && (
                                <Badge variant="outline" className="text-xs">Internal</Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-700 whitespace-pre-wrap">{comment.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Simple comment form */}
                <div className="border-t pt-4">
                  <div className="space-y-3">
                    <textarea
                      placeholder="Add a comment..."
                      className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      rows={3}
                    />
                    <div className="flex justify-between items-center">
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="rounded" />
                        <span>Internal comment</span>
                      </label>
                      <Button size="sm">Add Comment</Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Priority */}
          <Card>
            <CardHeader>
              <CardTitle>Status & Priority</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1">
                  <Badge className={getStatusColor(ticket.status)}>
                    {ticket.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Priority</label>
                <div className="mt-1">
                  <Badge className={getPriorityColor(ticket.priority)}>
                    {ticket.priority}
                  </Badge>
                </div>
              </div>
              {ticket.category && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Category</label>
                  <div className="mt-1">
                    <Badge variant="outline">
                      <Tag className="w-3 h-3 mr-1" />
                      {ticket.category}
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* People */}
          <Card>
            <CardHeader>
              <CardTitle>People</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Created by</label>
                <div className="mt-1 flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium">
                    {getInitials(ticket.createdBy.name || '')}
                  </div>
                  <div>
                    <div className="text-sm font-medium">{ticket.createdBy.name}</div>
                    <div className="text-xs text-gray-500">{ticket.createdBy.email}</div>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Assigned to</label>
                <div className="mt-1">
                  {ticket.assignedTo ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-sm font-medium text-white">
                        {getInitials(ticket.assignedTo.name || '')}
                      </div>
                      <div>
                        <div className="text-sm font-medium">{ticket.assignedTo.name}</div>
                        <div className="text-xs text-gray-500">{ticket.assignedTo.email}</div>
                      </div>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">Unassigned</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2 text-sm">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600">Created:</span>
                <span>{formatDateTime(ticket.createdAt)}</span>
              </div>
              
              <div className="flex items-center space-x-2 text-sm">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600">Updated:</span>
                <span>{formatDateTime(ticket.updatedAt)}</span>
              </div>

              {ticket.resolvedAt && (
                <div className="flex items-center space-x-2 text-sm">
                  <Clock className="w-4 h-4 text-green-400" />
                  <span className="text-gray-600">Resolved:</span>
                  <span>{formatDateTime(ticket.resolvedAt)}</span>
                </div>
              )}

              {ticket.closedAt && (
                <div className="flex items-center space-x-2 text-sm">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">Closed:</span>
                  <span>{formatDateTime(ticket.closedAt)}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
