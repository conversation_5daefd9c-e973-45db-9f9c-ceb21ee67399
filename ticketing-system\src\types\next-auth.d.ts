import { UserRole } from '@prisma/client'
import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      role: UserRole
      department?: string | null
    }
  }

  interface User {
    role: UserRole
    department?: string | null
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    department?: string | null
  }
}
