'use client'

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function SeedPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleSeed = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/seed', {
        method: 'POST',
      })
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to seed database' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Database Seeding</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Click the button below to seed the database with sample data.
            </p>
            
            <Button 
              onClick={handleSeed} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Seeding...' : 'Seed Database'}
            </Button>

            {result && (
              <div className="mt-4 p-4 bg-gray-100 rounded-md">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}

            <div className="mt-6 text-sm text-gray-600">
              <h3 className="font-medium mb-2">Test Accounts:</h3>
              <ul className="space-y-1">
                <li><strong>Admin:</strong> <EMAIL> / admin123</li>
                <li><strong>Agent:</strong> <EMAIL> / agent123</li>
                <li><strong>User:</strong> <EMAIL> / user123</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
