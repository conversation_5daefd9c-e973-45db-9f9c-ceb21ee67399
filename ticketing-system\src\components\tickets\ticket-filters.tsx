'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON>, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { TicketStatus, TicketPriority, UserRole } from '@prisma/client'
import { Search, X } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
}

export function TicketFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  const [agents, setAgents] = useState<User[]>([])
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || '',
    priority: searchParams.get('priority') || '',
    assignedToId: searchParams.get('assignedToId') || '',
  })

  useEffect(() => {
    if (session?.user.role === UserRole.ADMIN || session?.user.role === UserRole.AGENT) {
      fetchAgents()
    }
  }, [session])

  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/users?role=AGENT')
      if (response.ok) {
        const data = await response.json()
        setAgents(data.users || [])
      }
    } catch (error) {
      console.error('Error fetching agents:', error)
    }
  }

  const applyFilters = () => {
    const params = new URLSearchParams()
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value)
      }
    })

    router.push(`/tickets?${params.toString()}`)
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      priority: '',
      assignedToId: '',
    })
    router.push('/tickets')
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== '')
  const canViewAllTickets = session?.user.role === UserRole.ADMIN || session?.user.role === UserRole.AGENT

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Filters</span>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="w-4 h-4 mr-1" />
              Clear
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search tickets..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              <SelectItem value={TicketStatus.OPEN}>Open</SelectItem>
              <SelectItem value={TicketStatus.IN_PROGRESS}>In Progress</SelectItem>
              <SelectItem value={TicketStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={TicketStatus.RESOLVED}>Resolved</SelectItem>
              <SelectItem value={TicketStatus.CLOSED}>Closed</SelectItem>
              <SelectItem value={TicketStatus.CANCELLED}>Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <Select value={filters.priority} onValueChange={(value) => handleFilterChange('priority', value)}>
            <SelectTrigger>
              <SelectValue placeholder="All priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All priorities</SelectItem>
              <SelectItem value={TicketPriority.LOW}>Low</SelectItem>
              <SelectItem value={TicketPriority.MEDIUM}>Medium</SelectItem>
              <SelectItem value={TicketPriority.HIGH}>High</SelectItem>
              <SelectItem value={TicketPriority.URGENT}>Urgent</SelectItem>
              <SelectItem value={TicketPriority.CRITICAL}>Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {canViewAllTickets && agents.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assigned Agent
            </label>
            <Select value={filters.assignedToId} onValueChange={(value) => handleFilterChange('assignedToId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All agents" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All agents</SelectItem>
                <SelectItem value="unassigned">Unassigned</SelectItem>
                {agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <Button onClick={applyFilters} className="w-full">
          Apply Filters
        </Button>

        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Filters</h4>
          <div className="space-y-2">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => {
                setFilters(prev => ({ ...prev, status: TicketStatus.OPEN }))
                const params = new URLSearchParams()
                params.set('status', TicketStatus.OPEN)
                router.push(`/tickets?${params.toString()}`)
              }}
            >
              Open Tickets
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => {
                setFilters(prev => ({ ...prev, assignedToId: session?.user.id || '' }))
                const params = new URLSearchParams()
                params.set('assignedToId', session?.user.id || '')
                router.push(`/tickets?${params.toString()}`)
              }}
            >
              My Assigned Tickets
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => {
                setFilters(prev => ({ ...prev, priority: TicketPriority.URGENT }))
                const params = new URLSearchParams()
                params.set('priority', TicketPriority.URGENT)
                router.push(`/tickets?${params.toString()}`)
              }}
            >
              Urgent Tickets
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
