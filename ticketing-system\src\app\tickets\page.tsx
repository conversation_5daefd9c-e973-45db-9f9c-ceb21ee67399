import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { TicketsList } from '@/components/tickets/tickets-list'
import { TicketFilters } from '@/components/tickets/ticket-filters'
import { CreateTicketButton } from '@/components/tickets/create-ticket-button'

export default function TicketsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tickets</h1>
            <p className="text-gray-600">Manage and track support tickets</p>
          </div>
          <CreateTicketButton />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <TicketFilters />
          </div>
          <div className="lg:col-span-3">
            <TicketsList />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
