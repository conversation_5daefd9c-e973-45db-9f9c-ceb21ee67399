import { PrismaClient, UserRole, TicketStatus, TicketPriority } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create users
  const adminPassword = await bcrypt.hash('admin123', 12)
  const agentPassword = await bcrypt.hash('agent123', 12)
  const userPassword = await bcrypt.hash('user123', 12)

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'System Administrator',
      password: adminPassword,
      role: UserRole.ADMIN,
      department: 'IT',
      phone: '******-0101',
    },
  })

  const agent = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Support Agent',
      password: agentPassword,
      role: UserRole.AGENT,
      department: 'IT Support',
      phone: '******-0102',
    },
  })

  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'John Doe',
      password: userPassword,
      role: UserRole.USER,
      department: 'Marketing',
      phone: '******-0103',
    },
  })

  // Create sample tickets
  const tickets = [
    {
      title: 'Cannot access email account',
      description: 'I am unable to log into my email account. Getting authentication error.',
      status: TicketStatus.OPEN,
      priority: TicketPriority.HIGH,
      category: 'Email',
      createdById: user.id,
      assignedToId: agent.id,
    },
    {
      title: 'Printer not working',
      description: 'The office printer is not responding. Paper jam error showing.',
      status: TicketStatus.IN_PROGRESS,
      priority: TicketPriority.MEDIUM,
      category: 'Hardware',
      createdById: user.id,
      assignedToId: agent.id,
    },
    {
      title: 'Software installation request',
      description: 'Need Adobe Creative Suite installed on my workstation.',
      status: TicketStatus.PENDING,
      priority: TicketPriority.LOW,
      category: 'Software',
      createdById: user.id,
    },
    {
      title: 'Network connectivity issues',
      description: 'Intermittent network disconnections throughout the day.',
      status: TicketStatus.RESOLVED,
      priority: TicketPriority.URGENT,
      category: 'Network',
      createdById: user.id,
      assignedToId: agent.id,
      resolvedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    },
    {
      title: 'Password reset request',
      description: 'Need to reset my Windows login password.',
      status: TicketStatus.CLOSED,
      priority: TicketPriority.MEDIUM,
      category: 'Account',
      createdById: user.id,
      assignedToId: agent.id,
      resolvedAt: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2 days ago
      closedAt: new Date(Date.now() - 47 * 60 * 60 * 1000), // 2 days ago
    },
  ]

  for (const ticketData of tickets) {
    const ticket = await prisma.ticket.create({
      data: ticketData,
    })

    // Add some comments to tickets
    if (ticket.status !== TicketStatus.OPEN) {
      await prisma.comment.create({
        data: {
          content: 'Thank you for reporting this issue. I will look into it.',
          ticketId: ticket.id,
          authorId: agent.id,
          isInternal: false,
        },
      })

      if (ticket.status === TicketStatus.RESOLVED || ticket.status === TicketStatus.CLOSED) {
        await prisma.comment.create({
          data: {
            content: 'Issue has been resolved. Please let me know if you need further assistance.',
            ticketId: ticket.id,
            authorId: agent.id,
            isInternal: false,
          },
        })
      }
    }
  }

  console.log('✅ Database seeded successfully!')
  console.log('\n📧 Test accounts created:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('Agent: <EMAIL> / agent123')
  console.log('User: <EMAIL> / user123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
