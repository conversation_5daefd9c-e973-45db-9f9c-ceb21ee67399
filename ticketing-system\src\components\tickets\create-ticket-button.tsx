'use client'

import { useState } from 'react'
import { Plus } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { CreateTicketModal } from './create-ticket-modal'

export function CreateTicketButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        <Plus className="w-4 h-4 mr-2" />
        Create Ticket
      </Button>
      <CreateTicketModal 
        isOpen={isOpen} 
        onClose={() => setIsOpen(false)} 
      />
    </>
  )
}
