import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { TicketStatus, TicketPriority, UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const userRole = session.user.role

    // Base where clause for user role restrictions
    const baseWhere = userRole === UserRole.USER ? { createdById: userId } : {}

    const [
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      myTickets,
      urgentTickets,
    ] = await Promise.all([
      // Total tickets
      db.ticket.count({ where: baseWhere }),
      
      // Open tickets
      db.ticket.count({
        where: {
          ...baseWhere,
          status: TicketStatus.OPEN,
        },
      }),
      
      // In progress tickets
      db.ticket.count({
        where: {
          ...baseWhere,
          status: TicketStatus.IN_PROGRESS,
        },
      }),
      
      // Resolved tickets
      db.ticket.count({
        where: {
          ...baseWhere,
          status: TicketStatus.RESOLVED,
        },
      }),
      
      // My tickets (created by me or assigned to me)
      db.ticket.count({
        where: {
          OR: [
            { createdById: userId },
            { assignedToId: userId },
          ],
        },
      }),
      
      // Urgent tickets
      db.ticket.count({
        where: {
          ...baseWhere,
          priority: {
            in: [TicketPriority.URGENT, TicketPriority.CRITICAL],
          },
        },
      }),
    ])

    return NextResponse.json({
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      myTickets,
      urgentTickets,
    })
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
