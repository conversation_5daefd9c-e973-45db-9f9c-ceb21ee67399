import { User, Ticket, Comment, Attachment, User<PERSON><PERSON>, Ticket<PERSON>tatus, TicketPriority } from '@prisma/client'

// Extended types with relations
export type TicketWithRelations = Ticket & {
  createdBy: User
  assignedTo: User | null
  comments: (Comment & { author: User })[]
  attachments: Attachment[]
  _count?: {
    comments: number
    attachments: number
  }
}

export type CommentWithAuthor = Comment & {
  author: User
}

export type UserWithCounts = User & {
  _count?: {
    createdTickets: number
    assignedTickets: number
    comments: number
  }
}

// Form types
export interface CreateTicketData {
  title: string
  description: string
  priority: TicketPriority
  category?: string
  assignedToId?: string
  dueDate?: Date
  tags?: string[]
}

export interface UpdateTicketData {
  title?: string
  description?: string
  status?: TicketStatus
  priority?: TicketPriority
  category?: string
  assignedToId?: string
  dueDate?: Date
  tags?: string[]
}

export interface CreateCommentData {
  content: string
  isInternal?: boolean
}

export interface CreateUserData {
  email: string
  name: string
  password: string
  role?: UserRole
  department?: string
  phone?: string
}

// Dashboard types
export interface DashboardStats {
  totalTickets: number
  openTickets: number
  inProgressTickets: number
  resolvedTickets: number
  myTickets: number
  myAssignedTickets: number
  urgentTickets: number
  overdueTickets: number
}

export interface TicketsByStatus {
  status: TicketStatus
  count: number
}

export interface TicketsByPriority {
  priority: TicketPriority
  count: number
}

// Filter and search types
export interface TicketFilters {
  status?: TicketStatus[]
  priority?: TicketPriority[]
  assignedToId?: string
  createdById?: string
  category?: string
  dateRange?: {
    from: Date
    to: Date
  }
  search?: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Export Prisma types
export {
  User,
  Ticket,
  Comment,
  Attachment,
  UserRole,
  TicketStatus,
  TicketPriority,
}
