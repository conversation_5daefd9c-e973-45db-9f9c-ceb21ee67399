import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { TicketStatus, UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userRole = session.user.role
    const userId = session.user.id

    // Base where clause for user role restrictions
    const baseWhere = userRole === UserRole.USER ? { createdById: userId } : {}

    const statusCounts = await db.ticket.groupBy({
      by: ['status'],
      where: baseWhere,
      _count: {
        status: true,
      },
    })

    const statusColors = {
      [TicketStatus.OPEN]: '#3b82f6',
      [TicketStatus.IN_PROGRESS]: '#f59e0b',
      [TicketStatus.PENDING]: '#f97316',
      [TicketStatus.RESOLVED]: '#10b981',
      [TicketStatus.CLOSED]: '#6b7280',
      [TicketStatus.CANCELLED]: '#ef4444',
    }

    const chartData = statusCounts.map(item => ({
      status: item.status,
      count: item._count.status,
      color: statusColors[item.status] || '#6b7280',
    }))

    return NextResponse.json(chartData)
  } catch (error) {
    console.error('Error fetching chart data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
