import { getServerSession } from 'next-auth/next'
import { authOptions } from './auth'
import { UserRole } from '@prisma/client'
import { redirect } from 'next/navigation'

export async function getCurrentUser() {
  const session = await getServerSession(authOptions)
  return session?.user
}

export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    redirect('/auth/signin')
  }
  return user
}

export async function requireRole(allowedRoles: UserRole[]) {
  const user = await requireAuth()
  if (!allowedRoles.includes(user.role)) {
    redirect('/unauthorized')
  }
  return user
}

export async function requireAdmin() {
  return requireRole([UserRole.ADMIN])
}

export async function requireAgentOrAdmin() {
  return requireRole([UserRole.AGENT, UserRole.ADMIN])
}

export function hasPermission(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole)
}

export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}

export function isAgent(userRole: UserRole): boolean {
  return userRole === UserRole.AGENT
}

export function isUser(userRole: UserRole): boolean {
  return userRole === UserRole.USER
}

export function canManageTickets(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.AGENT
}

export function canViewAllTickets(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.AGENT
}

export function canAssignTickets(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.AGENT
}

export function canManageUsers(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}
